import { error } from "../../../../../../tool/localize/src/panel"

export const HERO_FOOT_WIDTH = 20 // Hero脚部碰撞宽度（左右各延伸20px）
export enum LandType {
    None = "none",
    Normal = "normal",
    Die = "die",
    Water = "water",
    Rebirth = "rebirth",
}

// Hero轨迹信息
export interface HeroTrajectory {
    // 左脚线段位置
    leftFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 右脚线段位置
    rightFoot: { start: cc.Vec2, end: cc.Vec2 }
    // 世界矩形
    worldRect: cc.Rect
}

// 碰撞结果
export interface CollisionResult {
    point: cc.Vec2      // 碰撞点
    distance: number    // 碰撞距离（用于选择最近碰撞）
    normal?: cc.Vec2    // 碰撞法线（可选）
}

export interface LandCollider extends cc.Component {
    type: LandType
    node: cc.Node

    intersectWith: (trajectory: HeroTrajectory) => CollisionResult | null

    getLeftMax(): number
    getRightMax(): number
}

class BaseLandCollider extends cc.Component implements LandCollider {
    type: LandType = LandType.None
    node: cc.Node = null

    public intersectWith(_trajectory: HeroTrajectory): CollisionResult | null { return null }

    public getLeftMax(): number { throw new Error("Method not implemented.") }
    public getRightMax(): number { throw new Error("Method not implemented.") }
}

export class NormalLandCollider extends BaseLandCollider {
    type: LandType = LandType.Normal

    private lineNode: cc.Node = null
    private startPos: cc.Vec2
    private endPos: cc.Vec2

    onLoad(): void {
        this.lineNode = this.node.Child("line")
        if (this.lineNode) {
            this.startPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2(-this.lineNode.anchorX * this.lineNode.width, 0))
            this.endPos = ut.convertToNodeAR(this.lineNode, this.node.parent.parent, cc.v2((1 - this.lineNode.anchorX) * this.lineNode.width, 0))
        }
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (!this.lineNode) {
            console.error(`NormalLandCollider 检测失败,因为不存在可停留线段,name:${this.node.name}`)
            return null
        }
        const intersectPos = cc.v2()
        const trajectories = [trajectory.rightFoot, trajectory.leftFoot]
        for (let i = 0; i < trajectories.length; i++) {
            const traj = trajectories[i]
            if (ut.lineLine(traj.start, traj.end, this.startPos, this.endPos, intersectPos)) {
                if (!intersectPos.fuzzyEquals(traj.start, 0.01)) {
                    intersectPos.x += i == 0 ? -HERO_FOOT_WIDTH : HERO_FOOT_WIDTH
                    return {
                        point: intersectPos,
                        distance: traj.start.sub(intersectPos).mag()
                    }
                }
            }
        }
        return null
    }

    public getLeftMax(): number {
        return this.startPos.x
    }

    public getRightMax(): number {
        return this.endPos.x
    }
}

// 重生地块
export class RebirthLandCollider extends NormalLandCollider {
    type: LandType = LandType.Rebirth

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(trajectory)
    }
}

// 瀑布地块
export class WaterLandCollider extends NormalLandCollider {
    type: LandType = LandType.Water

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        return super.intersectWith(trajectory)
    }
}

// 一碰就死地块
export class DieLandCollider extends BaseLandCollider {
    type: LandType = LandType.Die
    worldPoints: cc.Vec2[] = null

    public init() {
        const collider = this.node.getComponent(cc.PolygonCollider)
        if (!collider) {
            throw new Error(`DieLandCollider 检测失败,因为不存在碰撞器,name:${this.node.name}`)
        }
        this.worldPoints = collider.points.map(point => this.node.convertToWorldSpaceAR(cc.v2(point.x, point.y)))
    }

    public intersectWith(trajectory: HeroTrajectory): CollisionResult | null {
        if (cc.Intersection.rectPolygon(trajectory.worldRect, this.worldPoints)) {
            const collisionPoint = this.calculateCollisionPoint(heroRect, polygonPoints);
            const distance = heroNode.getPosition().sub(collisionPoint).mag();
            return {
                point: collisionPoint,
                distance: distance
            }
        }
        return null
    }

    public getLeftMax(): number {
        return this.node.x - this.node.width >> 1
    }
    public getRightMax(): number {
        return this.node.x + this.node.width >> 1
    }
}

export class LandColliderFactory {
    private static colliderMap = new Map<LandType, new () => LandCollider>([
        [LandType.Normal, NormalLandCollider],
        [LandType.Die, DieLandCollider],
        [LandType.Water, WaterLandCollider],
        [LandType.Rebirth, RebirthLandCollider],
    ])

    static createCollider(point: cc.Node, index: number, totalCount: number): LandCollider {
        let landType = this.determineLandType(point, index, totalCount)
        const ColliderClass = this.colliderMap.get(landType) || NormalLandCollider
        return point.addComponent(ColliderClass)
    }

    private static determineLandType(point: cc.Node, index: number, totalCount: number): LandType {
        const nodeName = point.name.toLowerCase()
        if (nodeName.includes(LandType.Die)) return LandType.Die
        if (nodeName.includes(LandType.Water)) return LandType.Water
        if (nodeName.includes(LandType.Rebirth)) return LandType.Rebirth

        return LandType.Normal
    }
}
